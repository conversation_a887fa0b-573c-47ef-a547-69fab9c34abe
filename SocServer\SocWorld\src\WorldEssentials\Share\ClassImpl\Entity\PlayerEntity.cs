using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Utility;

namespace WizardGames.Soc.Common.Entity
{
    public partial class PlayerEntity
    {
        public PlayerDebugComponent DebugComp => GetComponent<PlayerDebugComponent>(EComponentIdEnum.Debug);
        public PlayerConstructionComponent ConstructionComp => GetComponent<PlayerConstructionComponent>(EComponentIdEnum.PlayerConstruction);
        public PlayerSafeOfflineComponent SafeOfflineComp => GetComponent<PlayerSafeOfflineComponent>((int)EComponentIdEnum.PlayerSafeOffline);

        public ulong RoleId => (ulong)LongRoleId;



        public bool IsOffline => !IsOnline;

        public bool IsEquipEnable(int position) => ByteUtil.HasFlag(EquipEnableFlags, 1 << position);

        /// <summary>
        /// 玩家处于倒地
        /// </summary>
        public bool IsWounded => ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.Wounded);

        /// <summary>
        /// 玩家处于重伤倒地
        /// </summary>
        public bool IsIncapacitated => ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.Incapacitated);

        /// <summary>
        /// 玩家已死亡
        /// </summary>
        public bool IsDead => ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.Died);
        
        public bool HasDieFlag => ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.WaitDied) ||
                                  ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.Died) ||
                                  ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.DyingEnd);

        /// <summary>
        /// 玩家活着，倒地时也算活着
        /// </summary>
        public bool IsAlive => ByteUtil.HasFlag(LifeCycleFlags, LifeFlags.Alive);

        public bool WasAided => this.AidedSourceEntityId > 0;

        public MeleeCustom NullHand
        {
            get => NullHandCustom;
            set => NullHandCustom = value;
        }

        public IHeldItemEntity GetHeldItemByEntityId(long entityID)
        {
            if (NullHand != null)
            {
                if (entityID == 0 || NullHand.EntityId == entityID)
                    return NullHand;
            }
            if (CustomItem7 != null && CustomItem7.EntityId == entityID) return CustomItem7 as IHeldItemEntity;
            if (CustomItem8 != null && CustomItem8.EntityId == entityID) return CustomItem8 as IHeldItemEntity;
            if (CustomItem1 != null && CustomItem1.EntityId == entityID) return CustomItem1 as IHeldItemEntity;
            if (CustomItem2 != null && CustomItem2.EntityId == entityID) return CustomItem2 as IHeldItemEntity;
            if (CustomItem3 != null && CustomItem3.EntityId == entityID) return CustomItem3 as IHeldItemEntity;
            if (CustomItem4 != null && CustomItem4.EntityId == entityID) return CustomItem4 as IHeldItemEntity;
            if (CustomItem5 != null && CustomItem5.EntityId == entityID) return CustomItem5 as IHeldItemEntity;
            if (CustomItem6 != null && CustomItem6.EntityId == entityID) return CustomItem6 as IHeldItemEntity;
            if (CustomItemHiddenUse != null && CustomItemHiddenUse.EntityId == entityID) return CustomItemHiddenUse as IHeldItemEntity;
            if (CustomItemModels != null)
            {
                for (int i = 0; i < CustomItemModels.Count; i++)
                {
                    EmbeddedCustomBase embeddedCustomBase = CustomItemModels[i];
                    if (embeddedCustomBase != null && embeddedCustomBase.EntityId == entityID)
                    {
                        return CustomItemModels[i] as IHeldItemEntity;
                    }
                }
            }

            //logger.InfoFormat("PlayerEntity:{0},内嵌item： {1} 不存在", this.EntityId, entityID);
            return null;
        }

        /// <summary>
        /// 当前武器表id
        /// </summary>
        public long CurrentWeaponTableId
        {
            get => GetHeldItemByEntityId(CurrentWeaponId)?.TableId ?? 0; /* GetCurrentHandEntity<IHeldItemEntity>(bLocalPlayer)?.TableId ?? 0;*/
        }


        public void SetEquipEnableFlag(int position, bool enable)
        {
            var value = 1 << position;
            if (enable)
                EquipEnableFlags |= value;
            else
                EquipEnableFlags &= ~value;
        }
        
        public float Hp
        {
            get
            {
#if SOC_CLIENT
                return ReadonlyHp;
#else
                if (TryGetComponent(EComponentIdEnum.Damageable, out DamageableComponent dc))
                {
                    return dc.Hp;
                }
                return 0;
#endif
            }
        }

        public float MaxHp
        {
            get
            {
#if SOC_CLIENT
                return ReadonlyMaxHp;
#else
                if (TryGetComponent(EComponentIdEnum.Damageable, out DamageableComponent dc))
                {
                    return dc.MaxHp;
                }
                return 0;
#endif
            }
        }
    }
}
